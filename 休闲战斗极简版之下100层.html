<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗冒险</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let blueBalls = [], redBalls = [], blackBalls = [];
    let king; // 玩家控制的国王
    let playerHealth = 100, enemyHealth = 100;
    let maxPlayerHealth = 100, maxEnemyHealth = 100;
    let gameStarted = false;
    let currentLevel = 1, currentWave = 1;

    // 游戏状态
    let gamePhase = 'battle'; // 'platform' 或 'battle' - 调试模式：直接进入战斗
    let collectedSoldiers = []; // 收集的小兵
    let collectedSoldierBalls = []; // 收集的小兵球（会下落到地面）
    let coins = 0; // 收集的金币数量
    let platforms = []; // 平台数组
    let collectibles = []; // 可收集物品数组
    let cursors; // 键盘控制

    // 下一百层游戏机制
    let currentFloor = 1; // 当前层数
    let cameraScrollSpeed = 2; // 地图向上滚动速度
    let platformSpacing = 150; // 平台间距
    let nextPlatformY = 200; // 下一个平台的Y坐标
    let isScrolling = true; // 是否开启地图滚动
    let gameStartTime = 0; // 游戏开始时间
    let bossHoles = []; // boss洞口数组
    let lastBossFloor = 0; // 上一个boss洞口的层数

    // 碰撞控制变量 - 防止连锁反应bug
    let ballsToDestroy = []; // 待销毁的球列表
    let collisionCooldowns = new Map(); // 球的碰撞冷却时间
    let isTransforming = false; // 是否正在变身中

    // 现代混战管理系统
    class BattleManager {
        constructor() {
            this.units = new Map(); // 所有战斗单位的统一管理
            this.pendingActions = []; // 待处理的行动队列
            this.lastUpdateTime = 0;
            this.updateInterval = 50; // 每50ms更新一次，更流畅
            this.isProcessing = false; // 防止重入
            this.gameScene = null; // 保存游戏场景引用，用于动画
        }

        // 注册战斗单位
        registerUnit(unit, team) {
            if (!unit || !unit.x) return;
            const id = this.generateUnitId();
            unit.battleId = id;
            unit.team = team;
            unit.isAlive = true;
            unit.lastActionTime = 0;
            this.units.set(id, unit);
            return id;
        }

        // 移除战斗单位
        removeUnit(unitId) {
            if (this.units.has(unitId)) {
                const unit = this.units.get(unitId);
                unit.isAlive = false;
                this.units.delete(unitId);
            }
        }

        // 获取存活的敌方单位
        getEnemies(team) {
            const enemies = [];
            this.units.forEach(unit => {
                if (unit.team !== team && unit.isAlive && unit.health > 0) {
                    enemies.push(unit);
                }
            });
            return enemies;
        }

        // 获取存活的友方单位
        getAllies(team) {
            const allies = [];
            this.units.forEach(unit => {
                if (unit.team === team && unit.isAlive && unit.health > 0) {
                    allies.push(unit);
                }
            });
            return allies;
        }

        // 生成唯一ID
        generateUnitId() {
            return 'unit_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 安全的战斗更新
        update(currentTime) {
            if (this.isProcessing || currentTime - this.lastUpdateTime < this.updateInterval) {
                return;
            }

            this.isProcessing = true;
            this.lastUpdateTime = currentTime;

            try {
                // 清理死亡单位
                this.cleanupDeadUnits();

                // 更新血条位置
                this.updateHealthBars();

                // 处理战斗逻辑
                this.processBattleActions(currentTime);
            } catch (error) {
                console.warn('Error in battle update:', error);
            } finally {
                this.isProcessing = false;
            }
        }

        // 更新所有单位的血条
        updateHealthBars() {
            this.units.forEach(unit => {
                if (!unit.isAlive || !unit.healthBar || !unit.healthBarBg) return;

                try {
                    // 根据单位类型设置血条大小
                    let barWidth, barHeight, offsetY;

                    if (unit.maxHealth >= 20) {
                        // Boss血条（血量20+）
                        barWidth = 50;
                        barHeight = 8;
                        offsetY = -30;
                    } else {
                        // 普通小兵血条
                        barWidth = 24;
                        barHeight = 4;
                        offsetY = -18;
                    }

                    // 清除并重绘血条背景
                    if (unit.healthBarBg && unit.healthBarBg.clear) {
                        unit.healthBarBg.clear();
                        unit.healthBarBg.fillStyle(0x000000);
                        unit.healthBarBg.fillRect(unit.x - barWidth/2, unit.y + offsetY, barWidth, barHeight);
                    }

                    // 清除并重绘血条
                    if (unit.healthBar && unit.healthBar.clear) {
                        unit.healthBar.clear();
                        const healthPercent = unit.health / unit.maxHealth;
                        const healthColor = unit.team === 'blue' ? 0x00ff00 : 0xff0000;
                        unit.healthBar.fillStyle(healthColor);
                        unit.healthBar.fillRect(unit.x - barWidth/2, unit.y + offsetY, barWidth * healthPercent, barHeight);
                    }
                } catch (error) {
                    console.warn('Error updating health bar:', error);
                }
            });
        }

        // 清理死亡单位
        cleanupDeadUnits() {
            const deadUnits = [];
            this.units.forEach((unit, id) => {
                if (!unit.isAlive || unit.health <= 0 || !unit.active) {
                    deadUnits.push(id);
                }
            });
            deadUnits.forEach(id => this.removeUnit(id));
        }

        // 处理战斗行动
        processBattleActions(currentTime) {
            this.units.forEach(unit => {
                if (!unit.isAlive || unit.health <= 0 || unit.isDying) return;

                // 检查攻击冷却
                if (currentTime - unit.lastActionTime < (unit.attackCooldown || 1000)) return;

                // 寻找目标
                const enemies = this.getEnemies(unit.team);
                if (enemies.length === 0) return;

                const target = this.findNearestTarget(unit, enemies);
                if (!target) return;

                const distance = this.getDistance(unit, target);

                const attackRange = unit.attackRange || 50;

                if (distance <= attackRange) {
                    // 检查是否是国王（远程魔法攻击）
                    if (unit.label === 'king') {
                        this.executeMagicAttack(unit, target);
                    } else {
                        this.executeAttack(unit, target);
                    }
                    unit.lastActionTime = currentTime;
                } else {
                    // 移动（避免身体重叠）
                    this.moveTowardsTargetWithCollisionAvoidance(unit, target);
                }
            });
        }

        // 寻找最近目标
        findNearestTarget(unit, enemies) {
            let nearest = null;
            let minDistance = Infinity;

            enemies.forEach(enemy => {
                if (!enemy.isAlive || enemy.health <= 0 || enemy.isDying) return;
                const distance = this.getDistance(unit, enemy);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = enemy;
                }
            });

            return nearest;
        }

        // 计算距离
        getDistance(unit1, unit2) {
            if (!unit1 || !unit2 || unit1.x === undefined || unit2.x === undefined) return Infinity;
            return Phaser.Math.Distance.Between(unit1.x, unit1.y, unit2.x, unit2.y);
        }

        // 执行攻击
        executeAttack(attacker, target) {
            if (!attacker || !target || target.health <= 0 || target.isDying) return;

            // 标记目标正在被攻击，防止重复攻击
            if (target.isBeingAttacked) return;
            target.isBeingAttacked = true;

            // 执行武器挥动动画
            this.playAttackAnimation(attacker, target);

            // 延迟造成伤害，配合动画
            setTimeout(() => {
                if (target.isAlive && target.health > 0) {
                    this.dealDamage(target, attacker.attackPower || 1);
                }
                target.isBeingAttacked = false;
            }, 300); // 300ms后造成伤害，给动画时间
        }

        // 国王的魔法攻击
        executeMagicAttack(attacker, target) {
            if (!attacker || !target || target.health <= 0 || target.isDying) return;

            // 标记目标正在被攻击，防止重复攻击
            if (target.isBeingAttacked) return;
            target.isBeingAttacked = true;

            // 创建魔法弹特效
            this.createMagicProjectile(attacker, target);

            // 延迟造成伤害
            setTimeout(() => {
                if (target.isAlive && target.health > 0) {
                    this.dealDamage(target, attacker.attackPower || 3); // 国王伤害更高
                }
                target.isBeingAttacked = false;
            }, 500); // 魔法弹飞行时间
        }

        // 创建魔法弹特效
        createMagicProjectile(attacker, target) {
            if (!this.gameScene) return;

            try {
                // 创建魔法弹
                const projectile = this.gameScene.add.graphics();
                projectile.fillStyle(0x00ffff, 0.9); // 青色魔法弹
                projectile.fillCircle(0, 0, 6);
                projectile.lineStyle(2, 0xffffff, 0.8);
                projectile.strokeCircle(0, 0, 6);
                projectile.setPosition(attacker.x, attacker.y);
                projectile.setDepth(15); // 高层级，在所有角色上方

                // 魔法弹飞向目标
                this.gameScene.tweens.add({
                    targets: projectile,
                    x: target.x,
                    y: target.y,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击特效
                        const hitEffect = this.gameScene.add.graphics();
                        hitEffect.fillStyle(0x00ffff, 0.8);
                        hitEffect.fillCircle(target.x, target.y, 15);
                        hitEffect.lineStyle(3, 0xffffff, 1.0);
                        hitEffect.strokeCircle(target.x, target.y, 15);
                        hitEffect.setDepth(15);

                        // 攻击效果消失
                        this.gameScene.tweens.add({
                            targets: hitEffect,
                            alpha: 0,
                            scaleX: 2,
                            scaleY: 2,
                            duration: 300,
                            onComplete: () => hitEffect.destroy()
                        });

                        // 魔法弹消失
                        projectile.destroy();
                    }
                });
            } catch (error) {
                console.warn('Error creating magic projectile:', error);
            }
        }

        // 播放攻击动画
        playAttackAnimation(attacker, target) {
            if (!attacker.weapon || !this.gameScene) return;

            // 标记正在攻击
            attacker.isAttacking = true;

            // 计算攻击方向
            const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

            // 武器攻击动画：更明显的摆动幅度
            const baseAngle = angle;
            const swingAngle1 = baseAngle + Phaser.Math.DegToRad(-30); // 反向预备动作
            const swingAngle2 = baseAngle + Phaser.Math.DegToRad(60);  // 更大的攻击角度

            // 人物抖动动画
            const originalX = attacker.x;
            const originalY = attacker.y;

            // 第一段动画：反向预备动作
            this.gameScene.tweens.add({
                targets: attacker.weapon,
                rotation: swingAngle1,
                duration: 100,
                ease: 'Power2',
                onComplete: () => {
                    // 第二段动画：快速攻击到60度 + 人物抖动
                    this.gameScene.tweens.add({
                        targets: attacker.weapon,
                        rotation: swingAngle2,
                        duration: 120,
                        ease: 'Back.easeOut',
                        onComplete: () => {
                            // 第三段动画：回到中间位置
                            this.gameScene.tweens.add({
                                targets: attacker.weapon,
                                rotation: baseAngle + Phaser.Math.DegToRad(20),
                                duration: 100,
                                ease: 'Power2',
                                onComplete: () => {
                                    // 最后回到初始角度
                                    this.gameScene.tweens.add({
                                        targets: attacker.weapon,
                                        rotation: 0,
                                        duration: 150,
                                        ease: 'Power1',
                                        onComplete: () => {
                                            attacker.isAttacking = false;
                                        }
                                    });
                                }
                            });
                        }
                    });

                    // 人物抖动效果
                    this.gameScene.tweens.add({
                        targets: attacker,
                        x: originalX + Phaser.Math.Between(-3, 3),
                        y: originalY + Phaser.Math.Between(-3, 3),
                        duration: 50,
                        yoyo: true,
                        repeat: 2,
                        onComplete: () => {
                            // 恢复原位置
                            attacker.setPosition(originalX, originalY);
                        }
                    });
                }
            });
        }

        // 造成伤害
        dealDamage(target, damage) {
            if (!target || target.health <= 0 || target.isDying) return;

            target.health = Math.max(0, target.health - damage);

            // 创建伤害特效
            this.createDamageEffect(target, damage);

            if (target.health <= 0 && !target.isDying) {
                target.isDying = true;
                target.isAlive = false;

                // 创建击杀特效
                this.createKillEffect(target);

                // 延迟销毁，避免立即删除
                setTimeout(() => {
                    this.destroyUnit(target);

                    // 单位死亡后检查战斗结果
                    if (this.gameScene && typeof checkBattleResult === 'function') {
                        checkBattleResult.call(this.gameScene);
                    }
                }, 500);
            }
        }

        // 创建伤害数字特效
        createDamageEffect(target, damage) {
            if (!this.gameScene || !target) return;

            try {
                const damageText = this.gameScene.add.text(target.x, target.y - 30, `-${damage}`, {
                    fontSize: '12px',
                    fill: '#ff0000',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.gameScene.tweens.add({
                    targets: damageText,
                    y: target.y - 50,
                    alpha: 0,
                    duration: 800,
                    onComplete: () => damageText.destroy()
                });
            } catch (error) {
                console.warn('Error creating damage effect:', error);
            }
        }

        // 创建击杀特效
        createKillEffect(target) {
            if (!this.gameScene || !target) return;

            try {
                const killText = this.gameScene.add.text(target.x, target.y, 'KILL!', {
                    fontSize: '16px',
                    fill: '#ffff00',
                    fontWeight: 'bold',
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5);

                this.gameScene.tweens.add({
                    targets: killText,
                    y: target.y - 60,
                    alpha: 0,
                    scale: 1.5,
                    duration: 1000,
                    onComplete: () => killText.destroy()
                });
            } catch (error) {
                console.warn('Error creating kill effect:', error);
            }
        }

        // 销毁单位
        destroyUnit(unit) {
            if (!unit || unit.isDestroyed) return;
            unit.isDestroyed = true;

            // 从数组中安全移除
            this.removeFromArrays(unit);

            // 销毁游戏对象
            if (unit.weapon) unit.weapon.destroy();
            if (unit.healthBar) unit.healthBar.destroy();
            if (unit.healthBarBg) unit.healthBarBg.destroy();
            if (unit.active) unit.destroy();
        }

        // 从数组中移除单位
        removeFromArrays(unit) {
            try {


                // 安全地从数组中移除
                const arrays = [blueBalls, redBalls, blackBalls];
                arrays.forEach(arr => {
                    if (Array.isArray(arr)) {
                        const index = arr.indexOf(unit);
                        if (index > -1) {
                            arr.splice(index, 1);
                        }
                    }
                });
            } catch (error) {
                console.warn('Error removing unit from arrays:', error);
            }
        }



        // 移动向目标 - 防止身体重叠
        moveTowardsTargetWithCollisionAvoidance(unit, target) {
            if (!unit || !target || unit.isAttacking) return;

            const dx = target.x - unit.x;
            const dy = target.y - unit.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 0) {
                let moveX = (dx / distance);
                let moveY = (dy / distance);

                // 检查与其他单位的碰撞
                const avoidanceVector = this.calculateAvoidanceVector(unit);
                moveX += avoidanceVector.x;
                moveY += avoidanceVector.y;

                // 标准化移动向量
                const moveLength = Math.sqrt(moveX * moveX + moveY * moveY);
                if (moveLength > 0) {
                    moveX /= moveLength;
                    moveY /= moveLength;
                }

                const moveSpeed = 2.0;
                const newX = unit.x + moveX * moveSpeed;
                const newY = unit.y + moveY * moveSpeed;

                // 边界检查
                const boundedX = Math.max(70, Math.min(680, newX));
                const boundedY = Math.max(200, Math.min(1200, newY));

                unit.setPosition(boundedX, boundedY);
            }
        }

        // 计算避让向量，防止单位重叠
        calculateAvoidanceVector(unit) {
            let avoidX = 0;
            let avoidY = 0;
            const avoidanceRadius = 40; // 避让半径

            this.units.forEach(otherUnit => {
                if (otherUnit === unit || !otherUnit.isAlive || otherUnit.health <= 0) return;

                const dx = unit.x - otherUnit.x;
                const dy = unit.y - otherUnit.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < avoidanceRadius && distance > 0) {
                    // 计算避让力度，距离越近力度越大
                    const avoidanceStrength = (avoidanceRadius - distance) / avoidanceRadius;
                    avoidX += (dx / distance) * avoidanceStrength * 0.5;
                    avoidY += (dy / distance) * avoidanceStrength * 0.5;
                }
            });

            return { x: avoidX, y: avoidY };
        }

        // 获取战斗统计
        getBattleStats() {
            const stats = { blue: 0, red: 0 };
            this.units.forEach(unit => {
                if (unit.isAlive && unit.health > 0) {
                    if (unit.team === 'blue') {
                        stats.blue++;
                    } else if (unit.team === 'red') {
                        stats.red++;
                    }
                }
            });
            return stats;
        }
    }

    // 创建全局战斗管理器
    let battleManager = new BattleManager();

    // 确保数组安全初始化的函数
    function ensureArraysInitialized() {
        if (!Array.isArray(blueBalls)) blueBalls = [];
        if (!Array.isArray(redBalls)) redBalls = [];
        if (!Array.isArray(blackBalls)) blackBalls = [];
        if (!Array.isArray(ballsToDestroy)) ballsToDestroy = [];
    }

    // 游戏变量
    let battleMode = false; // 战斗模式



    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#87CEEB', // 天空蓝色背景
        physics: {
            default: 'matter',
            matter: {
                gravity: { y: 0.8 }, // 平台游戏需要重力
                debug: false
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载大背景图片
        this.load.image('gameBackground', 'images/rpg/background9.jpg');

        // 创建带高光效果的球纹理
        createBallTexture.call(this, 'blueBall', 0x3498db);
        createBallTexture.call(this, 'redBall', 0xe74c3c);
        createBallTexture.call(this, 'blackBall', 0x000000);

        // 加载小兵图片
        this.load.image('blueSoldier', 'images/rpg/蓝色小兵.png');
        this.load.image('redSoldier', 'images/rpg/红色小兵.png');
        this.load.image('weapon', 'images/rpg/小兵的武器.png');

        // 加载特殊角色图片
        this.load.image('king', 'images/rpg/国王.png');
        this.load.image('kingWeapon', 'images/rpg/国王的武器.png');
        this.load.image('redLeader', 'images/rpg/红色将领.png');

        // 创建平台游戏资源
        createPlatformTextures.call(this);
    }

    // 创建球的纹理（带高光效果）
    function createBallTexture(textureKey, color) {
        const ballGraphic = this.add.graphics();

        // 主球体
        ballGraphic.fillStyle(color, 1);
        ballGraphic.fillCircle(16, 16, 15);

        // 添加高光效果
        ballGraphic.fillStyle(0xffffff, 0.4);
        ballGraphic.fillCircle(11, 11, 5); // 主高光

        // 添加次高光
        ballGraphic.fillStyle(0xffffff, 0.2);
        ballGraphic.fillCircle(9, 9, 2);

        // 添加阴影效果
        ballGraphic.fillStyle(0x000000, 0.2);
        ballGraphic.fillCircle(20, 20, 3);

        ballGraphic.generateTexture(textureKey, 32, 32);
        ballGraphic.destroy(); // 销毁图形对象，避免显示在画布上
    }

    // 创建平台游戏纹理
    function createPlatformTextures() {
        // 创建平台纹理
        const platformGraphic = this.add.graphics();
        platformGraphic.fillStyle(0x8B4513, 1); // 棕色平台
        platformGraphic.fillRect(0, 0, 200, 32);
        platformGraphic.lineStyle(2, 0x654321, 1);
        platformGraphic.strokeRect(0, 0, 200, 32);
        platformGraphic.generateTexture('platform', 200, 32);
        platformGraphic.destroy();

        // 创建金币纹理
        const coinGraphic = this.add.graphics();
        coinGraphic.fillStyle(0xFFD700, 1); // 金色
        coinGraphic.fillCircle(16, 16, 15);
        coinGraphic.lineStyle(2, 0xFFA500, 1);
        coinGraphic.strokeCircle(16, 16, 15);
        // 添加内部细节
        coinGraphic.fillStyle(0xFFA500, 0.8);
        coinGraphic.fillCircle(16, 16, 10);
        coinGraphic.generateTexture('coin', 32, 32);
        coinGraphic.destroy();

        // 创建可收集小兵纹理（小一点的蓝色球）
        const collectibleSoldierGraphic = this.add.graphics();
        collectibleSoldierGraphic.fillStyle(0x3498db, 1);
        collectibleSoldierGraphic.fillCircle(12, 12, 10);
        collectibleSoldierGraphic.fillStyle(0xffffff, 0.4);
        collectibleSoldierGraphic.fillCircle(8, 8, 3);
        collectibleSoldierGraphic.generateTexture('collectibleSoldier', 24, 24);
        collectibleSoldierGraphic.destroy();

        // 创建数字门纹理
        createNumberGateTextures.call(this);

        // 创建boss洞口纹理
        createBossHoleTexture.call(this);
    }

    // 创建数字门纹理
    function createNumberGateTextures() {
        // +2门 - 扁的门框设计
        const plus2Gate = this.add.graphics();
        // 左柱子
        plus2Gate.fillStyle(0x008000, 1);
        plus2Gate.fillRect(0, 0, 15, 100);
        // 右柱子
        plus2Gate.fillRect(85, 0, 15, 100);
        // 顶部横梁
        plus2Gate.fillStyle(0x00FF00, 0.8);
        plus2Gate.fillRect(15, 0, 70, 25);
        plus2Gate.lineStyle(3, 0x008000, 1);
        plus2Gate.strokeRect(15, 0, 70, 25);
        plus2Gate.generateTexture('plus2Gate', 100, 100);
        plus2Gate.destroy();

        // x2门
        const x2Gate = this.add.graphics();
        // 左柱子
        x2Gate.fillStyle(0xCC5500, 1);
        x2Gate.fillRect(0, 0, 15, 100);
        // 右柱子
        x2Gate.fillRect(85, 0, 15, 100);
        // 顶部横梁
        x2Gate.fillStyle(0xFF6600, 0.8);
        x2Gate.fillRect(15, 0, 70, 25);
        x2Gate.lineStyle(3, 0xCC5500, 1);
        x2Gate.strokeRect(15, 0, 70, 25);
        x2Gate.generateTexture('x2Gate', 100, 100);
        x2Gate.destroy();

        // +5门
        const plus5Gate = this.add.graphics();
        // 左柱子
        plus5Gate.fillStyle(0x0044CC, 1);
        plus5Gate.fillRect(0, 0, 15, 100);
        // 右柱子
        plus5Gate.fillRect(85, 0, 15, 100);
        // 顶部横梁
        plus5Gate.fillStyle(0x0066FF, 0.8);
        plus5Gate.fillRect(15, 0, 70, 25);
        plus5Gate.lineStyle(3, 0x0044CC, 1);
        plus5Gate.strokeRect(15, 0, 70, 25);
        plus5Gate.generateTexture('plus5Gate', 100, 100);
        plus5Gate.destroy();
    }

    // 创建boss洞口纹理
    function createBossHoleTexture() {
        const bossHole = this.add.graphics();

        // 创建洞口外圈（深色边框）
        bossHole.fillStyle(0x000000, 1);
        bossHole.fillCircle(50, 50, 48);

        // 创建洞口内圈（深红色）
        bossHole.fillStyle(0x8B0000, 1);
        bossHole.fillCircle(50, 50, 40);

        // 创建洞口中心（黑色深渊）
        bossHole.fillStyle(0x000000, 1);
        bossHole.fillCircle(50, 50, 30);

        // 添加一些光效
        bossHole.fillStyle(0xFF0000, 0.3);
        bossHole.fillCircle(50, 50, 35);

        bossHole.generateTexture('bossHole', 100, 100);
        bossHole.destroy();
    }





    // 创建游戏场景
    function create() {
        // 确保数组正确初始化
        ensureArraysInitialized();

        // 根据游戏阶段创建不同场景
        if (gamePhase === 'platform') {
            createPlatformScene.call(this);
        } else if (gamePhase === 'battle') {
            createBattleScene.call(this);
        }

        gameStarted = true;
    }

    // 创建平台游戏场景
    function createPlatformScene() {
        // 创建平台游戏的国王角色
        createPlatformKing.call(this);

        // 创建平台
        createPlatforms.call(this);

        // 创建平台游戏UI
        createPlatformUI.call(this);

        // 设置平台游戏控制
        setupPlatformControls.call(this);

        // 设置游戏开始时间
        gameStartTime = Date.now();

        console.log('平台游戏场景创建完成');
    }

    // 创建战斗场景
    function createBattleScene() {
        // 添加大背景图片
        const background = this.add.image(375, 667, 'gameBackground');
        background.setDisplaySize(750, 1334);
        background.setDepth(-10);

        // 调试模式：添加一些测试小兵
        if (collectedSoldiers.length === 0) {
            for (let i = 0; i < 5; i++) {
                collectedSoldiers.push({
                    type: 'blueSoldier',
                    followIndex: i
                });
            }
            console.log('调试模式：添加了5个测试小兵');
        }

        // 创建游戏边界
        createGameBounds.call(this);

        // 直接开始地面战斗
        startGroundBattle.call(this);

        // 创建战斗UI
        createBattleUI.call(this);

        console.log('战斗场景创建完成');
    }

    // 创建平台游戏的国王角色
    function createPlatformKing() {
        // 创建国王作为可控制的角色，在屏幕中央
        king = this.matter.add.image(375, 600, 'king');
        king.setScale(0.6); // 国王标准大小
        king.setDepth(10);

        // 设置物理属性 - 碰撞体匹配视觉大小
        const collisionWidth = 32 * 0.6; // 匹配缩放后的宽度
        const collisionHeight = 48 * 0.6; // 匹配缩放后的高度
        king.setRectangle(collisionWidth, collisionHeight);

        // 调整碰撞体的偏移，让国王站在平台上而不是嵌入平台
        king.body.render.sprite.yOffset = 0.1;
        king.setBounce(0.2);
        king.setFriction(0.8);
        king.setFrictionAir(0.01);
        king.setFixedRotation(); // 防止旋转

        // 设置角色属性
        king.health = 100;
        king.maxHealth = 100;
        king.isOnGround = false;
        king.moveSpeed = 5;
        king.jumpPower = 15;

        // 设置摄像机跟随国王
        this.cameras.main.startFollow(king);
        this.cameras.main.setFollowOffset(0, -200); // 让国王在屏幕上方一些

        // 创建国王头上的小球数字显示
        king.ballCountText = this.add.text(king.x, king.y - 100, '0', {
            fontSize: '40px',
            fill: '#FFD700',
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5).setDepth(15);

        console.log('平台游戏国王创建完成');
    }

    // 创建平台 - 下一百层风格
    function createPlatforms() {
        platforms = [];

        // 创建起始平台
        createSinglePlatform.call(this, 375, 700, 200);

        // 创建初始的几个平台
        for (let i = 1; i <= 10; i++) {
            const y = 700 + (i * platformSpacing);
            createRandomPlatform.call(this, y, i);
        }

        console.log('下一百层平台创建完成');
    }

    // 创建单个平台
    function createSinglePlatform(x, y, width) {
        const platform = this.matter.add.rectangle(x, y, width, 32, { isStatic: true });
        const platformSprite = this.add.image(x, y, 'platform');
        platformSprite.setDisplaySize(width, 32);
        platformSprite.setDepth(1);
        platforms.push({ body: platform, sprite: platformSprite, x: x, y: y, width: width });
        return platform;
    }

    // 创建随机平台
    function createRandomPlatform(y, floorNumber) {
        // 随机平台位置和宽度
        const minWidth = 120;
        const maxWidth = 250;
        const width = Phaser.Math.Between(minWidth, maxWidth);
        const x = Phaser.Math.Between(width/2 + 50, 750 - width/2 - 50);

        // 创建平台
        createSinglePlatform.call(this, x, y, width);

        // 检查是否应该创建boss洞口（每20层一个）
        if (floorNumber > 0 && floorNumber % 20 === 0 && floorNumber > lastBossFloor) {
            console.log(`准备创建第${floorNumber}层boss洞口，lastBossFloor=${lastBossFloor}`);
            createBossHole.call(this, x, y - 50, floorNumber);
            lastBossFloor = floorNumber;
        } else {
            // 在平台上随机放置物品
            createPlatformItems.call(this, x, y - 50, width, floorNumber);
        }
    }

    // 在平台上创建随机物品
    function createPlatformItems(platformX, itemY, platformWidth, floorNumber) {
        // 每个平台只有一个道具或没有道具
        const itemType = Phaser.Math.Between(1, 100);

        // 计算物品位置（在平台中央）
        const itemX = platformX;

        if (itemType <= 25) {
            // 25% 概率生成金币
            const item = this.matter.add.image(itemX, itemY, 'coin');
            item.setScale(1.0); // 稍微大一点
            item.setDepth(5);
            item.setSensor(true);
            item.setStatic(true);
            item.type = 'coin';
            item.collected = false;
            collectibles.push(item);

        } else if (itemType <= 50) {
            // 25% 概率生成小兵球
            const item = this.matter.add.image(itemX, itemY, 'blueBall');
            item.setScale(1.2); // 小球大一点
            item.setDepth(5);
            item.setSensor(true);
            item.setStatic(true);
            item.type = 'soldier';
            item.collected = false;
            collectibles.push(item);

        } else if (itemType <= 70) {
            // 20% 概率生成数字门
            const gateTypes = ['plus2', 'x2', 'plus5'];
            const gateType = gateTypes[Phaser.Math.Between(0, 2)];
            createNumberGate.call(this, itemX, itemY, gateType);
        }
        // 30% 概率不生成任何物品
    }

    // 创建数字门
    function createNumberGate(x, y, type) {
        let gateTexture, gateText, gateEffect;

        if (type === 'plus2') {
            gateTexture = 'plus2Gate';
            gateText = '+2';
            gateEffect = 'add2';
        } else if (type === 'x2') {
            gateTexture = 'x2Gate';
            gateText = '×2';
            gateEffect = 'multiply2';
        } else if (type === 'plus5') {
            gateTexture = 'plus5Gate';
            gateText = '+5';
            gateEffect = 'add5';
        }

        const gate = this.matter.add.image(x, y, gateTexture);
        gate.setScale(0.8); // 稍微小一点
        gate.setDepth(3);
        gate.setSensor(true);
        gate.setStatic(true);
        gate.type = 'numberGate';
        gate.effect = gateEffect;
        gate.collected = false;

        // 添加文字标签
        const gateLabel = this.add.text(x, y, gateText, {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setDepth(4);

        gate.label = gateLabel;
        collectibles.push(gate);
    }

    // 创建boss洞口
    function createBossHole(x, y, floorNumber) {

        // 创建boss洞口
        const bossHole = this.matter.add.image(x, y, 'bossHole');
        bossHole.setScale(1.5);
        bossHole.setDepth(3);
        bossHole.setSensor(true);
        bossHole.setStatic(true);
        bossHole.type = 'bossHole';
        bossHole.floorNumber = floorNumber;
        bossHole.collected = false;

        // 添加文字标签
        const holeLabel = this.add.text(x, y, `第${floorNumber}层\nBOSS`, {
            fontSize: '16px',
            fill: '#FFFFFF',
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 2,
            align: 'center'
        }).setOrigin(0.5).setDepth(4);

        bossHole.label = holeLabel;
        bossHoles.push(bossHole);
        collectibles.push(bossHole);

        console.log(`创建第${floorNumber}层boss洞口`);
    }

    // 创建平台游戏UI
    function createPlatformUI() {
        // 创建顶部UI背景 - 固定在屏幕上
        const topUIBg = this.add.graphics();
        topUIBg.fillStyle(0x2c3e50, 0.8);
        topUIBg.fillRect(0, 0, 750, 120);
        topUIBg.setDepth(100).setScrollFactor(0);

        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db);
        playerAvatarBg.lineStyle(3, 0x000000);
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40);
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);
        playerAvatarBg.setDepth(101).setScrollFactor(0);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '40px'
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0);

        const playerLabel = this.add.text(105, 20, '国王', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setDepth(102).setScrollFactor(0);

        // 金币计数器
        this.coinText = this.add.text(105, 45, `金币: ${coins}`, {
            fontSize: '18px',
            fill: '#FFD700',
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 1
        }).setDepth(102).setScrollFactor(0);

        // 小兵计数器
        this.soldierText = this.add.text(105, 70, `小兵: ${collectedSoldiers.length}`, {
            fontSize: '18px',
            fill: '#3498db',
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 1
        }).setDepth(102).setScrollFactor(0);

        // 关卡信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8);
        levelBg.lineStyle(2, 0x000000);
        levelBg.fillRoundedRect(300, 15, 150, 60, 10);
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);
        levelBg.setDepth(101).setScrollFactor(0);

        this.levelText = this.add.text(375, 35, `第 ${currentFloor} 层`, {
            fontSize: '20px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0);

        // 右上角目标信息
        const targetInfoBg = this.add.graphics();
        targetInfoBg.fillStyle(0x8B0000, 0.8);
        targetInfoBg.lineStyle(2, 0x000000);
        targetInfoBg.fillRoundedRect(550, 15, 180, 80, 10);
        targetInfoBg.strokeRoundedRect(550, 15, 180, 80, 10);
        targetInfoBg.setDepth(101).setScrollFactor(0);

        const targetLabel = this.add.text(640, 25, '目标', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0);

        this.targetText = this.add.text(640, 45, '到达第100层', {
            fontSize: '14px',
            fill: '#ff6666',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0);

        this.progressText = this.add.text(640, 65, `进度: ${currentFloor}/100`, {
            fontSize: '14px',
            fill: '#ff3333',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0);

        // 游戏提示
        this.hintText = this.add.text(375, 55, '向下跳跃，收集物品，挑战100层！', {
            fontSize: '14px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0);

        // 控制提示 - 固定在屏幕上
        this.controlText = this.add.text(375, 100, '方向键移动，空格键跳跃', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setDepth(102).setScrollFactor(0); // 固定在屏幕上
    }

    // 设置平台游戏控制
    function setupPlatformControls() {
        // 创建键盘控制
        cursors = this.input.keyboard.createCursorKeys();

        // 添加WASD控制
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');

        // 添加空格键控制
        this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);

        // 设置碰撞检测
        this.matter.world.on('collisionstart', handlePlatformCollision.bind(this));

        console.log('平台游戏控制设置完成');
    }

    // 处理平台游戏碰撞
    function handlePlatformCollision(event) {
        const pairs = event.pairs;

        for (let pair of pairs) {
            const bodyA = pair.bodyA;
            const bodyB = pair.bodyB;

            // 检查国王与可收集物品的碰撞（包括数字门）
            if ((bodyA.gameObject === king && bodyB.gameObject && bodyB.gameObject.type) ||
                (bodyB.gameObject === king && bodyA.gameObject && bodyA.gameObject.type)) {

                const collectible = bodyA.gameObject === king ? bodyB.gameObject : bodyA.gameObject;

                if (!collectible.collected) {
                    collectItem.call(this, collectible);
                }
            }

            // 检查国王与平台的碰撞
            if (bodyA.gameObject === king || bodyB.gameObject === king) {
                const otherBody = bodyA.gameObject === king ? bodyB : bodyA;

                // 检查是否是平台碰撞
                const platform = platforms.find(p => p.body === otherBody || p === otherBody);
                if (platform) {
                    // 检查碰撞方向，只有从上方碰撞才算站立
                    const platformY = platform.y || otherBody.position.y;
                    const kingCollisionHeight = 48 * 0.6; // 国王碰撞体高度
                    const kingBottom = king.y + (kingCollisionHeight / 2);
                    const platformTop = platformY - 16; // 平台高度的一半

                    if (kingBottom <= platformTop + 8) { // 允许一些容差
                        king.isOnGround = true;
                    }
                }
            }
        }
    }

    // 收集物品
    function collectItem(item) {
        item.collected = true;

        if (item.type === 'numberGate') {
            // 处理数字门效果
            handleNumberGateEffect.call(this, item);
        } else if (item.type === 'coin') {
            coins++;
            this.coinText.setText(`金币: ${coins}`);

            // 创建收集特效
            const collectEffect = this.add.text(item.x, item.y, '+1', {
                fontSize: '20px',
                fill: '#FFD700',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: collectEffect,
                y: item.y - 50,
                alpha: 0,
                duration: 1000,
                onComplete: () => collectEffect.destroy()
            });

        } else if (item.type === 'soldier') {
            // 直接增加小兵数量，不创建物理球
            collectedSoldiers.push({
                type: 'blueSoldier',
                followIndex: collectedSoldiers.length
            });
            this.soldierText.setText(`小兵: ${collectedSoldiers.length}`);

            // 更新国王头上的数字
            if (king.ballCountText) {
                king.ballCountText.setText(collectedSoldiers.length.toString());
            }

            // 创建收集特效
            const collectEffect = this.add.text(item.x, item.y, '小兵+1', {
                fontSize: '16px',
                fill: '#3498db',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: collectEffect,
                y: item.y - 50,
                alpha: 0,
                duration: 1000,
                onComplete: () => collectEffect.destroy()
            });

        } else if (item.type === 'numberGate') {
            // 处理数字门效果
            handleNumberGateEffect.call(this, item);
            // 数字门不消失，只是标记为已使用
            item.collected = true;
            setTimeout(() => {
                item.collected = false;
            }, 2000);
            return; // 数字门不销毁

        } else if (item.type === 'bossHole') {
            // 进入boss洞口，开始战斗
            enterBossHole.call(this, item);
            return; // boss洞口不销毁
        }

        // 销毁收集的物品（小球消失）
        if (item.label && item.label.destroy) {
            item.label.destroy();
        }
        item.destroy();

        // 从收集物品数组中移除
        const index = collectibles.indexOf(item);
        if (index > -1) {
            collectibles.splice(index, 1);
        }

        console.log(`收集了 ${item.type}, 金币: ${coins}, 小兵: ${collectedSoldiers.length}`);
    }

    // 处理数字门效果
    function handleNumberGateEffect(gate) {
        let effectText = '';
        let effectColor = '#FFFFFF';

        if (gate.effect === 'add2') {
            // +2 小兵
            for (let i = 0; i < 2; i++) {
                collectedSoldiers.push({
                    type: 'blueSoldier',
                    followIndex: collectedSoldiers.length
                });
            }
            effectText = '+2 小兵!';
            effectColor = '#00FF00';

        } else if (gate.effect === 'multiply2') {
            // x2 当前小兵数量
            const currentCount = collectedSoldiers.length;
            for (let i = 0; i < currentCount; i++) {
                collectedSoldiers.push({
                    type: 'blueSoldier',
                    followIndex: collectedSoldiers.length
                });
            }
            effectText = '×2 小兵!';
            effectColor = '#FF6600';

        } else if (gate.effect === 'add5') {
            // +5 小兵
            for (let i = 0; i < 5; i++) {
                collectedSoldiers.push({
                    type: 'blueSoldier',
                    followIndex: collectedSoldiers.length
                });
            }
            effectText = '+5 小兵!';
            effectColor = '#0066FF';
        }

        // 更新UI
        this.soldierText.setText(`小兵: ${collectedSoldiers.length}`);

        // 更新国王头上的数字
        if (king.ballCountText) {
            king.ballCountText.setText(collectedSoldiers.length.toString());
        }

        // 创建特效文字
        const gateEffect = this.add.text(gate.x, gate.y, effectText, {
            fontSize: '20px',
            fill: effectColor,
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        this.tweens.add({
            targets: gateEffect,
            y: gate.y - 80,
            alpha: 0,
            scale: 1.5,
            duration: 1500,
            onComplete: () => gateEffect.destroy()
        });

        console.log(`通过数字门: ${gate.effect}, 当前小兵数: ${collectedSoldiers.length}`);
    }

    // 进入boss洞口
    function enterBossHole(bossHole) {
        console.log(`进入第${bossHole.floorNumber}层boss洞口！`);

        // 显示进入提示
        if (this.hintText) {
            this.hintText.setText(`进入第${bossHole.floorNumber}层boss战斗！`);
            this.hintText.setStyle({ fill: '#ff0000' });
        }

        // 停止滚动
        isScrolling = false;

        // 1秒后进入战斗
        setTimeout(() => {
            transitionToBattle.call(this, bossHole.floorNumber);
        }, 1000);
    }

    function update() {
        if (gameStarted) {
            if (gamePhase === 'platform') {
                updatePlatformGame.call(this);
            } else if (gamePhase === 'battle') {
                updateBattleGame.call(this);
            }
        }
    }

    // 更新平台游戏
    function updatePlatformGame() {
        if (!king || !king.active) return;

        // 检查国王是否在地面上（通过速度判断）
        if (king.body && Math.abs(king.body.velocity.y) < 0.1) {
            king.isOnGround = true;
        } else {
            king.isOnGround = false;
        }

        // 处理键盘输入
        if (cursors.left.isDown || this.wasd.A.isDown) {
            king.setVelocityX(-king.moveSpeed);
        } else if (cursors.right.isDown || this.wasd.D.isDown) {
            king.setVelocityX(king.moveSpeed);
        } else {
            king.setVelocityX(king.body.velocity.x * 0.8); // 摩擦力
        }

        // 跳跃
        if ((cursors.up.isDown || this.wasd.W.isDown || this.spaceKey.isDown) && king.isOnGround) {
            king.setVelocityY(-king.jumpPower);
            king.isOnGround = false; // 跳跃后立即设为false，防止连续跳跃
        }

        // 更新层数显示
        updateFloorDisplay.call(this);

        // 动态生成新平台
        generateNewPlatforms.call(this);

        // 清理远离的平台和物品
        cleanupDistantObjects.call(this);

        // 检查游戏失败条件和胜利条件
        checkGameFailure.call(this);

        // 更新国王头上的数字显示位置
        if (king.ballCountText) {
            king.ballCountText.setPosition(king.x, king.y - 80);
        }

        // 检查游戏失败条件
        checkGameFailure.call(this);

        // 更新跟随的小兵位置
        updateFollowingSoldiers.call(this);
    }

    // 更新战斗游戏
    function updateBattleGame() {
        // 确保数组安全
        ensureArraysInitialized();

        updateHealthBars.call(this);
        checkGameState.call(this);

        // 处理待销毁的球 - 防止连锁反应
        processBallDestruction.call(this);

        // 在战斗模式下，使用新的战斗管理器
        if (battleMode && !isTransforming) {
            try {
                // 使用战斗管理器进行更新，而不是每帧执行复杂逻辑
                battleManager.update(this.time.now);
                updateWeaponPositions.call(this);
            } catch (error) {
                console.warn('Error in battle mode update:', error);
            }
        }
    }

    // 更新层数显示
    function updateFloorDisplay() {
        // 根据国王的实际Y坐标计算层数
        const startY = 600; // 起始位置
        const newFloor = Math.max(1, Math.floor((king.y - startY) / platformSpacing) + 1);

        if (newFloor !== currentFloor) {
            currentFloor = newFloor;
            console.log(`到达第 ${currentFloor} 层`);
        }

        // 更新UI显示
        if (this.levelText) {
            this.levelText.setText(`第 ${currentFloor} 层`);
        }
        if (this.progressText) {
            this.progressText.setText(`进度: ${currentFloor}/100`);
        }
    }

    // 动态生成新平台
    function generateNewPlatforms() {
        // 当国王下降时，在下方生成新平台
        const kingY = king.y;
        const lowestPlatformY = Math.max(...platforms.map(p => p.y || 0));

        // 如果国王距离最低平台不远，生成新平台
        if (kingY > lowestPlatformY - 800) {
            for (let i = 1; i <= 3; i++) {
                const newY = lowestPlatformY + platformSpacing * i;
                const newFloor = Math.floor((newY - 600) / platformSpacing) + 1;
                createRandomPlatform.call(this, newY, newFloor);
            }
        }
    }

    // 清理远离的平台和物品
    function cleanupDistantObjects() {
        const cameraTop = this.cameras.main.scrollY;

        // 清理在摄像机上方太远的平台
        platforms = platforms.filter(platform => {
            if (platform.y < cameraTop - 500) {
                // 销毁平台
                if (platform.body && platform.body.destroy) {
                    platform.body.destroy();
                }
                if (platform.sprite && platform.sprite.destroy) {
                    platform.sprite.destroy();
                }
                return false;
            }
            return true;
        });

        // 清理距离太远的收集物品
        collectibles = collectibles.filter(item => {
            if (item.y < cameraTop - 500) {
                if (item.label && item.label.destroy) {
                    item.label.destroy();
                }
                if (item.destroy) {
                    item.destroy();
                }
                return false;
            }
            return true;
        });
    }

    // 检查游戏失败条件
    function checkGameFailure() {
        // 检查国王是否掉出摄像机视野太远
        const cameraBottom = this.cameras.main.scrollY + this.cameras.main.height;
        if (king.y > cameraBottom + 500) { // 如果国王掉出摄像机视野太远
            gameFailure.call(this);
        }

        // 检查是否到达100层
        if (currentFloor >= 100) {
            transitionToBattle.call(this);
        }
    }

    // 游戏失败，进入战斗模式
    function gameFailure() {
        console.log('游戏失败！进入战斗模式');

        // 显示失败提示
        if (this.hintText) {
            this.hintText.setText('挑战失败！进入战斗模式');
            this.hintText.setStyle({ fill: '#ff0000' });
        }

        // 等待1秒后进入战斗
        setTimeout(() => {
            transitionToBattle.call(this);
        }, 1000);
    }

    // 更新跟随的小兵位置
    function updateFollowingSoldiers() {
        // 这里可以添加小兵跟随国王的逻辑
        // 暂时简化，在战斗阶段再显示小兵
    }

    // 过渡到战斗阶段
    function transitionToBattle(bossFloor = 100) {
        console.log(`进入第${bossFloor}层boss战斗！`);

        // 切换到战斗阶段
        gamePhase = 'battle';

        // 停止摄像机跟随
        this.cameras.main.stopFollow();

        // 清理收集物品
        collectibles.forEach(item => {
            if (item.active) {
                item.destroy();
            }
        });
        collectibles = [];

        // 清理平台
        platforms.forEach(platform => {
            if (platform.body && platform.body.destroy) {
                platform.body.destroy();
            }
            if (platform.sprite && platform.sprite.destroy) {
                platform.sprite.destroy();
            }
        });
        platforms = [];

        // 创建战斗UI
        createBattleUI.call(this);

        // 更新UI提示
        this.hintText.setText(`第${bossFloor}层boss战斗开始！`);

        // 停止摄像机跟随，固定在战斗区域中央
        this.cameras.main.stopFollow();
        this.cameras.main.setScroll(0, 0); // 固定摄像机位置到战斗区域中央

        // 让国王移动到蓝方后排中央
        if (king && king.active) {
            king.setPosition(300, 1200); // 后排位置
            if (king.setVelocity) {
                king.setVelocity(0, 0);
            }
        }

        // 等待小兵球落地后开始变身
        setTimeout(() => {
            startGroundBattle.call(this);
        }, 2000);
    }

    // 开始地面战斗
    function startGroundBattle() {
        console.log('开始地面战斗！');

        // 生成敌方单位从右侧进入
        createEnemyUnitsFromRight.call(this);

        // 变身收集的小兵球为战斗单位
        transformCollectedSoldiers.call(this);

        // 变身国王为战斗单位
        transformKingForBattle.call(this);

        // 开始战斗模式
        setTimeout(() => {
            startBattleMode.call(this);
        }, 1000);
    }

    // 从右侧创建敌方单位
    function createEnemyUnitsFromRight() {
        // 清空敌方数组
        redBalls = [];
        blackBalls = [];

        // 创建敌方小兵从右侧进入
        const enemySoldierCount = Math.max(3, Math.floor(collectedSoldiers.length * 0.8)); // 敌方数量稍少于玩家

        for (let i = 0; i < enemySoldierCount; i++) {
            // 创建红色小兵在上方阵营
            const maxPerRow = 6; // 每行最多6个小兵
            const row = Math.floor(i / maxPerRow);
            const col = i % maxPerRow;
            const spacing = 80;

            const startX = 375 - (Math.min(enemySoldierCount, maxPerRow) - 1) * spacing / 2;
            const soldierX = startX + (col * spacing);
            const soldierY = 400 + (row * 60); // 上方阵营

            const redSoldier = this.add.image(soldierX, soldierY, 'redSoldier');
            redSoldier.setScale(0.35); // 小兵统一大小，比国王和boss小
            redSoldier.setDepth(5);
            redSoldier.label = 'redSoldier';
            redSoldier.lastCollisionTime = 0;
            redSoldier.markedForDestroy = false;
            redSoldier.isPhysicsEnabled = false; // 标记为非物理对象

            // 添加武器
            const redWeapon = this.add.image(soldierX, soldierY, 'weapon');
            redWeapon.setScale(0.35); // 武器大小匹配小兵
            redWeapon.setDepth(6);
            redSoldier.weapon = redWeapon;

            // 设置移动动画，让小兵走到前排
            const frontLineX = 550 - i * 60; // 红方前排位置
            const frontLineY = 1150; // 前排Y坐标

            this.tweens.add({
                targets: redSoldier,
                x: frontLineX,
                y: frontLineY,
                duration: 2000 + i * 200,
                ease: 'Power2'
            });

            this.tweens.add({
                targets: redWeapon,
                x: frontLineX,
                y: frontLineY,
                duration: 2000 + i * 200,
                ease: 'Power2'
            });

            redBalls.push(redSoldier);
        }

        // 创建1-2个红色将领boss
        const bossCount = Math.min(2, Math.max(1, Math.floor(collectedSoldiers.length / 3)));

        for (let i = 0; i < bossCount; i++) {
            const bossX = 375 + (i - (bossCount - 1) / 2) * 100; // 居中排列boss
            const bossY = 300; // 上方后排

            const redLeader = this.add.image(bossX, bossY, 'redLeader');
            redLeader.setScale(0.6); // boss和国王一样大
            redLeader.setDepth(5);
            redLeader.label = 'redLeader';
            redLeader.lastCollisionTime = 0;
            redLeader.markedForDestroy = false;
            redLeader.isPhysicsEnabled = false; // 标记为非物理对象

            // boss直接在最终位置，不需要移动动画
            const backLineX = bossX;
            const backLineY = bossY;

            this.tweens.add({
                targets: redLeader,
                x: backLineX,
                y: backLineY,
                duration: 2500 + i * 300,
                ease: 'Power2'
            });

            blackBalls.push(redLeader);
        }

        console.log(`创建了${enemySoldierCount}个红色小兵和${bossCount}个红色将领`);
    }

    // 变身收集的小兵为战斗单位
    function transformCollectedSoldiers() {
        // 根据收集的小兵数量创建战斗单位
        collectedSoldiers.forEach((soldier, index) => {
            // 计算蓝方小兵阵形位置（国王前方，上下阵型）
            const frontLineY = 1000; // 蓝方前排，在国王前方
            const spacing = 80; // 小兵间距
            const maxPerRow = 6; // 每行最多6个小兵
            const row = Math.floor(index / maxPerRow);
            const col = index % maxPerRow;

            const startX = 375 - (Math.min(collectedSoldiers.length, maxPerRow) - 1) * spacing / 2; // 居中排列
            const soldierX = startX + (col * spacing);
            const soldierY = frontLineY - (row * 60); // 多行时向上排列

            // 创建蓝色小兵（纯图像，无物理效果）
            const blueSoldier = this.add.image(soldierX, soldierY, 'blueSoldier');
            blueSoldier.setScale(0.35); // 小兵统一大小，比国王和boss小
            blueSoldier.setDepth(5);
            blueSoldier.label = 'blueSoldier';
            blueSoldier.lastCollisionTime = 0;
            blueSoldier.markedForDestroy = false;
            blueSoldier.isPhysicsEnabled = false; // 标记为非物理对象

            // 添加武器
            const blueWeapon = this.add.image(soldierX, frontLineY, 'weapon');
            blueWeapon.setScale(0.35); // 武器大小匹配小兵
            blueWeapon.setDepth(6);
            blueSoldier.weapon = blueWeapon;

            // 设置战斗属性
            blueSoldier.health = 5;
            blueSoldier.maxHealth = 5;
            blueSoldier.team = 'blue';
            blueSoldier.attackPower = 1;
            blueSoldier.attackRange = 40;
            blueSoldier.lastAttackTime = 0;
            blueSoldier.attackCooldown = 1500;
            blueSoldier.isAttacking = false;

            // 创建血条
            const healthBarBg = this.add.graphics();
            healthBarBg.fillStyle(0x000000);
            healthBarBg.fillRect(soldierX - 12, soldierY - 18, 24, 4);
            healthBarBg.setDepth(10);

            const healthBar = this.add.graphics();
            healthBar.fillStyle(0x00ff00);
            healthBar.fillRect(soldierX - 12, soldierY - 18, 24, 4);
            healthBar.setDepth(11);

            blueSoldier.healthBarBg = healthBarBg;
            blueSoldier.healthBar = healthBar;

            // 添加到蓝方数组
            blueBalls.push(blueSoldier);
        });

        console.log(`${blueBalls.length}个收集的小兵变身为蓝色战斗单位`);
    }

    // 创建新的战斗国王
    function transformKingForBattle() {
        // 隐藏原来的平台国王
        if (king && king.active) {
            king.setVisible(false);
            if (king.ballCountText) {
                king.ballCountText.setVisible(false);
            }
        }

        // 创建新的战斗国王 - 下方阵营
        const battleKing = this.add.image(375, 1100, 'king');
        battleKing.setScale(0.6);
        battleKing.setDepth(5);
        battleKing.label = 'king';
        battleKing.team = 'blue';
        battleKing.health = 20;
        battleKing.maxHealth = 20;
        battleKing.attackPower = 3;
        battleKing.attackRange = 150;
        battleKing.lastAttackTime = 0;
        battleKing.attackCooldown = 1000;
        battleKing.isAttacking = false;
        battleKing.lastCollisionTime = 0;
        battleKing.markedForDestroy = false;
        battleKing.isPhysicsEnabled = false;

        // 创建国王的武器
        const kingWeapon = this.add.image(375, 1100, 'kingWeapon');
        kingWeapon.setScale(0.6);
        kingWeapon.setDepth(6);
        kingWeapon.setRotation(0);
        battleKing.weapon = kingWeapon;

        // 创建国王的血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(375 - 15, 1100 - 25, 30, 5);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0x00ff00);
        healthBar.fillRect(375 - 15, 1100 - 25, 30, 5);
        healthBar.setDepth(11);

        battleKing.healthBarBg = healthBarBg;
        battleKing.healthBar = healthBar;

        // 将战斗国王添加到蓝方数组
        blueBalls.push(battleKing);

        console.log('创建新的战斗国王');
    }

    // 清理数组中的null和无效值
    function cleanupArrays() {
        try {
            // 确保数组存在，如果不存在则初始化为空数组
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];

            const originalBlueLength = blueBalls.length;
            const originalRedLength = redBalls.length;
            const originalBlackLength = blackBalls.length;

            blueBalls = blueBalls.filter(unit => unit !== null && unit !== undefined && unit.active);
            redBalls = redBalls.filter(unit => unit !== null && unit !== undefined && unit.active);
            blackBalls = blackBalls.filter(unit => unit !== null && unit !== undefined && unit.active);

            // 如果有清理，输出日志
            if (originalBlueLength !== blueBalls.length) {
                console.log(`清理蓝球数组: ${originalBlueLength} -> ${blueBalls.length}`);
            }
            if (originalRedLength !== redBalls.length) {
                console.log(`清理红球数组: ${originalRedLength} -> ${redBalls.length}`);
            }
            if (originalBlackLength !== blackBalls.length) {
                console.log(`清理黑球数组: ${originalBlackLength} -> ${blackBalls.length}`);
            }
        } catch (error) {
            console.warn('Error cleaning arrays:', error);
            // 如果出错，重新初始化数组
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];
        }
    }

    // 创建游戏边界
    function createGameBounds() {
        // 创建简单的游戏边界
        // 左边界
        this.matter.add.rectangle(25, 667, 50, 1334, { isStatic: true });
        // 右边界
        this.matter.add.rectangle(725, 667, 50, 1334, { isStatic: true });
        // 上边界
        this.matter.add.rectangle(375, 25, 750, 50, { isStatic: true });
        // 下边界
        this.matter.add.rectangle(375, 1309, 750, 50, { isStatic: true });

        // 添加可视化地板
        createVisualFloors.call(this);
    }

    // 创建可视化地板
    function createVisualFloors() {
        const floorGraphics = this.add.graphics();

        // 地面地板
        floorGraphics.fillStyle(0x8B4513, 1); // 棕色地板
        floorGraphics.fillRect(0, 1284, 750, 50);
        floorGraphics.lineStyle(3, 0x654321, 1);
        floorGraphics.strokeRect(0, 1284, 750, 50);

        // 左墙地板
        floorGraphics.fillStyle(0x696969, 1); // 灰色墙壁
        floorGraphics.fillRect(0, 0, 50, 1334);
        floorGraphics.lineStyle(3, 0x2F4F4F, 1);
        floorGraphics.strokeRect(0, 0, 50, 1334);

        // 右墙地板
        floorGraphics.fillStyle(0x696969, 1);
        floorGraphics.fillRect(700, 0, 50, 1334);
        floorGraphics.lineStyle(3, 0x2F4F4F, 1);
        floorGraphics.strokeRect(700, 0, 50, 1334);

        // 天花板
        floorGraphics.fillStyle(0x87CEEB, 1); // 天空蓝
        floorGraphics.fillRect(0, 0, 750, 50);
        floorGraphics.lineStyle(3, 0x4682B4, 1);
        floorGraphics.strokeRect(0, 0, 750, 50);
    }



    // 创建敌方球（仅在战斗阶段使用）
    function createEnemyBalls() {
        // 重置关卡变量
        battleMode = false;
        ballsToDestroy = [];

        // 清空蓝方数组，因为蓝方单位来自收集的小兵
        blueBalls = [];

        // 创建三角形摆放的球 - 混合排列
        const ballPositions = [
            // 第一排（1个球）
            { x: 375, y: 400 },
            // 第二排（2个球）
            { x: 355, y: 440 },
            { x: 395, y: 440 },
            // 第三排（3个球）
            { x: 335, y: 480 },
            { x: 375, y: 480 },
            { x: 415, y: 480 },
            // 第四排（4个球）
            { x: 315, y: 520 },
            { x: 355, y: 520 },
            { x: 395, y: 520 },
            { x: 435, y: 520 },
            // 第五排（5个球）
            { x: 295, y: 560 },
            { x: 335, y: 560 },
            { x: 375, y: 560 },
            { x: 415, y: 560 },
            { x: 455, y: 560 }
        ];

        // 只创建敌方球（红球和黑球）
        const shuffledPositions = [...ballPositions].sort(() => Math.random() - 0.5);
        let blackBallCreated = false;

        for (let i = 0; i < Math.min(shuffledPositions.length, 10); i++) { // 减少敌方数量
            const pos = shuffledPositions[i];

            // 只创建红球和黑球
            let ballType, texture, label;

            if (!blackBallCreated && Math.random() < 0.2) { // 20%概率创建黑球
                ballType = 'black';
                texture = 'blackBall';
                label = 'blackBall';
                blackBallCreated = true;
            } else {
                ballType = 'red';
                texture = 'redBall';
                label = 'redBall';
            }

            const ball = this.matter.add.image(pos.x, pos.y, texture);
            ball.setScale(1.2); // 小球大一点
            ball.setCircle(16 * 1.2); // 碰撞体匹配视觉大小
            ball.setBounce(0.95); // 高弹性系数
            ball.setFriction(0.1);
            ball.setFrictionAir(0.008);
            ball.setDensity(0.001);
            ball.setDepth(5);
            ball.label = label;
            ball.lastCollisionTime = 0; // 上次碰撞时间
            ball.markedForDestroy = false; // 是否标记为待销毁

            if (ballType === 'red') {
                redBalls.push(ball);
            } else {
                blackBalls.push(ball);
            }
        }

        // 如果没有创建黑球，强制在最后一个位置创建一个
        if (!blackBallCreated && shuffledPositions.length > 0) {
            const lastPos = shuffledPositions[Math.min(14, shuffledPositions.length - 1)];

            // 如果最后位置已经有球，替换它
            if (blueBalls.length > 0) {
                const lastBlueBall = blueBalls.pop();
                lastBlueBall.destroy();
            } else if (redBalls.length > 0) {
                const lastRedBall = redBalls.pop();
                lastRedBall.destroy();
            }

            const blackBall = this.matter.add.image(lastPos.x, lastPos.y, 'blackBall');
            blackBall.setScale(1.2); // 小球大一点
            blackBall.setCircle(16 * 1.2); // 碰撞体匹配视觉大小
            blackBall.setBounce(0.95);
            blackBall.setFriction(0.1);
            blackBall.setFrictionAir(0.008);
            blackBall.setDensity(0.001);
            blackBall.setDepth(5);
            blackBall.label = 'blackBall';
            blackBall.lastCollisionTime = 0;
            blackBall.markedForDestroy = false;
            blackBalls.push(blackBall);
        }



        // 设置碰撞检测
        this.matter.world.on('collisionstart', handleCollision.bind(this));
    }

    // 创建战斗UI元素
    function createBattleUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);
        playerAvatarBg.setScrollFactor(0).setDepth(100);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '80px'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(101);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setScrollFactor(0).setDepth(101);

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);
        playerHealthBarBg.setScrollFactor(0).setDepth(100);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        this.playerHealthBar.setScrollFactor(0).setDepth(101);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(102);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);
        enemyAvatarBg.setScrollFactor(0).setDepth(100);

        const enemyAvatar = this.add.text(695, 60, '👹', {
            fontSize: '80px'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(101);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(101);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);
        healthBarBg.setScrollFactor(0).setDepth(100);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        this.enemyHealthBar.setScrollFactor(0).setDepth(101);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(102);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);
        levelBg.setScrollFactor(0).setDepth(100);

        this.levelText = this.add.text(375, 35, `Boss战斗`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(101);

        // 每关只有1个波次，不显示波次信息

        // 关卡显示
        this.floorText = this.add.text(375, 120, `第 ${currentFloor} 层`, {
            fontSize: '20px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(101);

        // 游戏状态文字
        this.statusText = this.add.text(375, 145, `使用收集的${collectedSoldiers.length}个小兵战斗！`, {
            fontSize: '16px',
            fill: '#ffff00',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setScrollFactor(0).setDepth(101);
    }

    // 设置输入控制
    function setupInput() {
        // 可以在这里添加其他输入控制
    }



    // 处理碰撞
    function handleCollision(event) {
        // 简化的碰撞处理，移除台球相关逻辑
        // 可以在这里添加其他碰撞逻辑
    }



    // 开始战斗模式
    function startBattleMode() {
        battleMode = true;

        // 更新状态提示
        if (this.hintText) {
            this.hintText.setText('战斗进行中！');
            this.hintText.setStyle({ fill: '#ff0000', fontSize: '18px' });
        }

        // 直接启动战斗系统
        startCombatSystem.call(this);

        console.log('战斗模式开始！');
    }

    // 启动战斗系统
    function startCombatSystem() {
        // 设置战斗管理器
        battleManager.gameScene = this;

        // 注册所有战斗单位
        registerBattleUnits.call(this);

        // 开始战斗循环
        isTransforming = false;

        console.log('战斗系统启动完成');
    }

    // 注册战斗单位
    function registerBattleUnits() {
        // 注册所有战斗单位到战斗管理器
        try {
            // 蓝方阵营：国王 + 收集的小兵球
            if (king && king.health > 0 && king.x !== undefined && king.y !== undefined) {
                try {
                    battleManager.registerUnit(king, 'blue');
                    king.isCombatReady = true;
                    console.log('注册蓝方国王');
                } catch (error) {
                    console.warn('Error registering king:', error);
                }
            }

            blueBalls.forEach(unit => {
                if (unit && unit.active && unit.x !== undefined && unit.y !== undefined) {
                    try {
                        // 为收集的小兵球设置战斗属性
                        unit.health = 5;
                        unit.maxHealth = 5;
                        unit.team = 'blue';
                        unit.attackPower = 1;
                        unit.attackRange = 50;
                        unit.lastAttackTime = 0;
                        unit.attackCooldown = 1500;
                        unit.isAttacking = false;

                        // 创建血条
                        unit.healthBarBg = this.add.graphics();
                        unit.healthBar = this.add.graphics();

                        battleManager.registerUnit(unit, 'blue');
                        unit.isCombatReady = true;
                    } catch (error) {
                        console.warn('Error registering blue soldier:', error);
                    }
                }
            });
            console.log(`注册蓝方小兵（收集的球）: ${blueBalls.length}个`);

            // 红方阵营：黑球boss + 红球小兵
            blackBalls.forEach(unit => {
                if (unit && unit.active) {
                    // 为红色将领设置战斗属性
                    unit.health = 15;
                    unit.maxHealth = 15;
                    unit.team = 'red';
                    unit.attackPower = 3;
                    unit.attackRange = 80;
                    unit.lastAttackTime = 0;
                    unit.attackCooldown = 1200;
                    unit.isAttacking = false;

                    // 创建血条
                    unit.healthBarBg = this.add.graphics();
                    unit.healthBar = this.add.graphics();

                    battleManager.registerUnit(unit, 'red'); // 红色将领属于红方
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册红方将领: ${blackBalls.length}个`);

            redBalls.forEach(unit => {
                if (unit && unit.active) {
                    // 为红色小兵设置战斗属性
                    unit.health = 5;
                    unit.maxHealth = 5;
                    unit.team = 'red';
                    unit.attackPower = 1;
                    unit.attackRange = 50;
                    unit.lastAttackTime = 0;
                    unit.attackCooldown = 1500;
                    unit.isAttacking = false;

                    // 创建血条
                    unit.healthBarBg = this.add.graphics();
                    unit.healthBar = this.add.graphics();

                    battleManager.registerUnit(unit, 'red');
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册红方小兵: ${redBalls.length}个`);

        } catch (error) {
            console.error('Error registering battle units:', error);
        }
    }









    // 启动战斗（简化版本）
    function startCombat() {
        console.log('战斗系统已通过startCombatSystem启动');
        // 这个函数现在由startCombatSystem替代

        // 创建新的小兵角色（仅图像，无物理效果）
        const soldier = this.add.image(soldierX, soldierY, 'blueSoldier');
        soldier.setScale(0.2);
        soldier.setDepth(5);
        soldier.setRotation(0); // 确保不倾斜
        soldier.label = 'blueBall';

        // 标记为非物理对象，战斗开始前不能移动
        soldier.isPhysicsEnabled = false;

        // 添加武器在身体右侧中间
        const weapon = this.add.image(soldierX + 8, soldierY, 'weapon');
        weapon.setScale(0.2);
        weapon.setDepth(6);
        weapon.setRotation(0);

        // 添加血条 - 小兵血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0x00ff00); // 蓝方用绿色血条
        healthBar.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBar.setDepth(11);

        soldier.weapon = weapon;
        soldier.healthBarBg = healthBarBg;
        soldier.healthBar = healthBar;
        soldier.maxHealth = 5; // 小兵血量提升到5
        soldier.health = 5;
        soldier.attackPower = 1;
        soldier.attackRange = 40;
        soldier.lastAttackTime = 0;
        soldier.attackCooldown = 1500;
        soldier.isAttacking = false;

        // 找到原球在数组中的位置并替换
        const currentIndex = blueBalls.indexOf(ball);
        if (currentIndex > -1) {
            blueBalls[currentIndex] = soldier;
        } else {
            blueBalls.push(soldier);
        }

        console.log('蓝色小兵变身完成（无物理效果）');
    }

    // 变身为红色小兵
    function transformToRedSoldier(ball, array, originalIndex) {
        if (!ball || !ball.active) return;

        // 设置红方小兵阵型：前排位置
        const frontLineY = 450; // 红方前排
        const spacing = 80; // 小兵间距
        const startX = 200 + (originalIndex * spacing); // 从左到右排列
        const soldierX = Math.min(startX, 550); // 限制在屏幕范围内
        const soldierY = frontLineY;

        // 安全销毁原球（包括其物理体）
        try {
            ball.destroy();
        } catch (error) {
            console.warn('Error destroying red soldier ball:', error);
        }

        // 创建新的红色小兵角色（仅图像，无物理效果）
        const soldier = this.add.image(soldierX, soldierY, 'redSoldier');
        soldier.setScale(0.35); // 统一小兵大小
        soldier.setDepth(5);
        soldier.setRotation(0); // 确保不倾斜
        soldier.label = 'redBall';

        // 标记为非物理对象，战斗开始前不能移动
        soldier.isPhysicsEnabled = false;

        // 添加武器在身体左侧中间
        const weapon = this.add.image(soldierX - 8, soldierY, 'weapon');
        weapon.setScale(0.35); // 武器大小匹配小兵
        weapon.setDepth(6);
        weapon.setRotation(0);

        // 添加血条 - 红方小兵血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0xff0000); // 红方用红色血条
        healthBar.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBar.setDepth(11);

        soldier.weapon = weapon;
        soldier.healthBarBg = healthBarBg;
        soldier.healthBar = healthBar;
        soldier.maxHealth = 5; // 小兵血量提升到5
        soldier.health = 5;
        soldier.attackPower = 1;
        soldier.attackRange = 40;
        soldier.lastAttackTime = 0;
        soldier.attackCooldown = 1500;
        soldier.isAttacking = false;

        // 找到原球在数组中的位置并替换
        const currentIndex = redBalls.indexOf(ball);
        if (currentIndex > -1) {
            redBalls[currentIndex] = soldier;
        } else {
            redBalls.push(soldier);
        }

        console.log('红色小兵变身完成（无物理效果）');
    }

    // 变身为红色将领
    function transformToRedLeader(ball, array, originalIndex) {
        if (!ball || !ball.active) return;

        // 设置红方将领阵型：后排位置
        const backLineY = 350; // 红方后排
        const spacing = 100; // 将领间距更大
        const startX = 300 + (originalIndex * spacing); // 从左到右排列
        const leaderX = Math.min(startX, 450); // 限制在屏幕范围内
        const leaderY = backLineY;

        // 安全销毁原球（包括其物理体）
        try {
            ball.destroy();
        } catch (error) {
            console.warn('Error destroying red leader ball:', error);
        }

        // 创建新的红色将领角色（仅图像，无物理效果）
        const leader = this.add.image(leaderX, leaderY, 'redLeader');
        leader.setScale(0.6); // boss和国王一样大
        leader.setDepth(5);
        leader.setRotation(0); // 确保不倾斜
        leader.label = 'blackBall';

        // 标记为非物理对象，战斗开始前不能移动
        leader.isPhysicsEnabled = false;

        // 添加武器在身体左侧中间
        const weapon = this.add.image(leaderX - 8, leaderY, 'weapon');
        weapon.setScale(0.6); // 武器大小匹配boss
        weapon.setDepth(6);
        weapon.setRotation(0);

        // 添加血条 - Boss血条（更大更显眼）
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(leaderX - 25, leaderY - 30, 50, 8); // Boss血条更大
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0xff0000); // Boss用红色血条
        healthBar.fillRect(leaderX - 25, leaderY - 30, 50, 8);
        healthBar.setDepth(11);

        leader.weapon = weapon;
        leader.healthBarBg = healthBarBg;
        leader.healthBar = healthBar;
        leader.maxHealth = 20; // Boss血量大幅提升到20
        leader.health = 20;
        leader.attackPower = 3; // Boss攻击力也提升
        leader.attackRange = 60; // Boss攻击范围更远
        leader.lastAttackTime = 0;
        leader.attackCooldown = 1000; // Boss攻击更快
        leader.isAttacking = false;

        // 找到原球在数组中的位置并替换
        const currentIndex = blackBalls.indexOf(ball);
        if (currentIndex > -1) {
            blackBalls[currentIndex] = leader;
        } else {
            blackBalls.push(leader);
        }

        console.log('红色将领变身完成（无物理效果）');
    }

    // 启动战斗
    function startCombat() {
        if (this.statusText && this.statusText.setText) {
            this.statusText.setText('战斗开始！');
            this.statusText.setStyle({ fill: '#ffff00', fontSize: '18px' });
        }

        // 确保战斗模式已启用
        battleMode = true;

        // 重置战斗管理器并设置游戏场景引用
        battleManager = new BattleManager();
        battleManager.gameScene = this; // 设置场景引用，用于动画

        // 注册所有战斗单位到战斗管理器
        try {
            // 蓝方阵营：国王 + 收集的小兵球
            if (king && king.health > 0) {
                battleManager.registerUnit(king, 'blue');
                king.isCombatReady = true;
                console.log('注册蓝方国王');
            }

            blueBalls.forEach(unit => {
                if (unit && unit.active) {
                    // 为收集的小兵球设置战斗属性
                    unit.health = 5;
                    unit.maxHealth = 5;
                    unit.team = 'blue';
                    unit.attackPower = 1;
                    unit.attackRange = 50;
                    unit.lastAttackTime = 0;
                    unit.attackCooldown = 1500;
                    unit.isAttacking = false;

                    battleManager.registerUnit(unit, 'blue');
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册蓝方小兵（收集的球）: ${blueBalls.length}个`);

            // 红方阵营：黑球 + 红球
            blackBalls.forEach(unit => {
                if (unit && unit.active) {
                    // 为黑球boss设置战斗属性
                    unit.health = 15;
                    unit.maxHealth = 15;
                    unit.team = 'red';
                    unit.attackPower = 3;
                    unit.attackRange = 80;
                    unit.lastAttackTime = 0;
                    unit.attackCooldown = 1200;
                    unit.isAttacking = false;

                    battleManager.registerUnit(unit, 'red'); // 黑球属于红方
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册红方将领（黑球）: ${blackBalls.length}个`);

            redBalls.forEach(unit => {
                if (unit && unit.active) {
                    // 为红球小兵设置战斗属性
                    unit.health = 5;
                    unit.maxHealth = 5;
                    unit.team = 'red';
                    unit.attackPower = 1;
                    unit.attackRange = 50;
                    unit.lastAttackTime = 0;
                    unit.attackCooldown = 1500;
                    unit.isAttacking = false;

                    battleManager.registerUnit(unit, 'red');
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册红方小兵（红球）: ${redBalls.length}个`);

            const stats = battleManager.getBattleStats();
            console.log(`战斗启动！蓝方（白球+蓝球）:${stats.blue}个单位, 红方（黑球+红球）:${stats.red}个单位`);
            console.log(`战斗模式状态: ${battleMode}, 变身状态: ${isTransforming}`);

            // 检查单方是否没有单位，直接判定胜负
            if (stats.blue === 0 && stats.red === 0) {
                if (this.statusText && this.statusText.setText) {
                    this.statusText.setText('双方都没有单位！平局');
                    this.statusText.setStyle({ fill: '#ffff00', fontSize: '20px' });
                }
                battleMode = false;
                return;
            } else if (stats.blue === 0) {
                if (this.statusText && this.statusText.setText) {
                    this.statusText.setText('蓝方没有单位！红方获胜');
                    this.statusText.setStyle({ fill: '#ff0000', fontSize: '20px' });
                }
                battleMode = false;
                return;
            } else if (stats.red === 0) {
                if (this.statusText && this.statusText.setText) {
                    this.statusText.setText('红方没有单位！蓝方获胜');
                    this.statusText.setStyle({ fill: '#0000ff', fontSize: '20px' });
                }
                battleMode = false;
                return;
            }

            // 双方都有单位，正常开始战斗
            console.log('双方都有单位，战斗管理器开始工作...');

        } catch (error) {
            console.warn('Error starting combat:', error);
        }
    }

    // 旧的攻击系统已被战斗管理器替代
    // 保留一些辅助函数供战斗管理器使用

    // 寻找最近的敌人
    function findNearestEnemy(attacker, enemies) {
        let nearestEnemy = null;
        let minDistance = Infinity;

        enemies.forEach(enemy => {
            // 确保敌人存在、活跃、有位置、有血量且没有正在死亡
            if (enemy && enemy.active && enemy.x !== undefined && enemy.health > 0 && !enemy.isDying) {
                const distance = Phaser.Math.Distance.Between(attacker.x, attacker.y, enemy.x, enemy.y);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemy;
                }
            }
        });

        return nearestEnemy;
    }

    // 移动向敌人 - 不使用物理效果，直接修改位置
    function moveTowardsEnemy(unit, enemy) {
        if (!enemy || unit.isAttacking || !unit.isCombatReady) return;

        // 计算移动方向
        const dx = enemy.x - unit.x;
        const dy = enemy.y - unit.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            // 标准化方向向量
            const normalizedDx = dx / distance;
            const normalizedDy = dy / distance;

            // 直接修改位置，不使用物理速度
            const moveSpeed = 1.5; // 每帧移动的像素数
            const newX = unit.x + normalizedDx * moveSpeed;
            const newY = unit.y + normalizedDy * moveSpeed;

            // 确保不移出边界
            const boundedX = Math.max(70, Math.min(680, newX));
            const boundedY = Math.max(200, Math.min(1200, newY));

            unit.setPosition(boundedX, boundedY);
        }
    }

    // 攻击敌人
    function attackEnemy(attacker, target) {
        // 检查攻击者和目标的状态
        if (!attacker || !attacker.weapon || attacker.isAttacking || attacker.health <= 0 || attacker.isDying) return;
        if (!target || target.health <= 0 || target.isDying || !target.active) return;

        // 计算攻击方向
        const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

        // 开始攻击动画
        attacker.isAttacking = true;

        // 武器攻击动画：更明显的摆动幅度
        const baseAngle = angle;
        const swingAngle1 = baseAngle + Phaser.Math.DegToRad(-30); // 反向预备动作
        const swingAngle2 = baseAngle + Phaser.Math.DegToRad(60);  // 更大的攻击角度

        // 人物抖动动画
        const originalX = attacker.x;
        const originalY = attacker.y;

        // 第一段动画：反向预备动作
        this.tweens.add({
            targets: attacker.weapon,
            rotation: swingAngle1,
            duration: 100,
            ease: 'Power2',
            onComplete: () => {
                // 第二段动画：快速攻击到60度 + 人物抖动
                this.tweens.add({
                    targets: attacker.weapon,
                    rotation: swingAngle2,
                    duration: 120,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        // 第三段动画：回到中间位置
                        this.tweens.add({
                            targets: attacker.weapon,
                            rotation: baseAngle + Phaser.Math.DegToRad(20),
                            duration: 100,
                            ease: 'Power2',
                            onComplete: () => {
                                // 最后回到初始角度
                                this.tweens.add({
                                    targets: attacker.weapon,
                                    rotation: 0,
                                    duration: 150,
                                    ease: 'Power1',
                                    onComplete: () => {
                                        attacker.isAttacking = false;
                                    }
                                });
                            }
                        });
                    }
                });

                // 人物抖动效果
                this.tweens.add({
                    targets: attacker,
                    x: originalX + Phaser.Math.Between(-3, 3),
                    y: originalY + Phaser.Math.Between(-3, 3),
                    duration: 50,
                    yoyo: true,
                    repeat: 2,
                    onComplete: () => {
                        // 恢复原位置
                        attacker.setPosition(originalX, originalY);
                    }
                });

                // 在快速攻击时造成伤害
                dealDamageToUnit.call(this, target, attacker.attackPower);
            }
        });
    }

    // 对单位造成伤害 - 简化版，主要用于特效
    function dealDamageToUnit(target, damage) {
        if (!target || target.health <= 0 || target.isDying) return;

        // 创建伤害数字特效
        try {
            const damageText = this.add.text(target.x, target.y - 30, `-${damage}`, {
                fontSize: '12px',
                fill: '#ff0000',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: damageText,
                y: target.y - 50,
                alpha: 0,
                duration: 800,
                onComplete: () => damageText.destroy()
            });
        } catch (error) {
            console.warn('Error creating damage effect:', error);
        }

        // 实际伤害处理由战斗管理器负责
        // 这里只负责视觉效果
    }

    // 销毁单位
    function destroyUnit(unit) {
        try {
            // 防止重复销毁
            if (!unit || unit.isDestroyed) return;
            unit.isDestroyed = true;

            // 销毁武器
            if (unit.weapon && unit.weapon.active) {
                unit.weapon.destroy();
                unit.weapon = null;
            }

            // 销毁血条
            if (unit.healthBar && unit.healthBar.active) {
                unit.healthBar.destroy();
                unit.healthBar = null;
            }
            if (unit.healthBarBg && unit.healthBarBg.active) {
                unit.healthBarBg.destroy();
                unit.healthBarBg = null;
            }

            // 从数组中移除
            if (unit === king) {
                king = null;
            } else {
                // 确保数组存在
                ensureArraysInitialized();

                const blueIndex = blueBalls.indexOf(unit);
                if (blueIndex > -1) {
                    blueBalls.splice(blueIndex, 1);
                    try {
                        console.log(`移除蓝色小兵，剩余: ${blueBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging blue unit removal');
                    }
                }

                const redIndex = redBalls.indexOf(unit);
                if (redIndex > -1) {
                    redBalls.splice(redIndex, 1);
                    try {
                        console.log(`移除红色小兵，剩余: ${redBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging red unit removal');
                    }
                }

                const blackIndex = blackBalls.indexOf(unit);
                if (blackIndex > -1) {
                    blackBalls.splice(blackIndex, 1);
                    try {
                        console.log(`移除红色将领，剩余: ${blackBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging black unit removal');
                    }
                }
            }

            // 清理数组中的null值
            try {
                if (Array.isArray(blueBalls)) {
                    blueBalls = blueBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
                if (Array.isArray(redBalls)) {
                    redBalls = redBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
                if (Array.isArray(blackBalls)) {
                    blackBalls = blackBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
            } catch (e) {
                console.warn('Error filtering arrays:', e);
            }

            // 销毁单位
            if (unit && unit.active) {
                unit.destroy();
            }
        } catch (error) {
            console.warn('Error destroying unit:', error);
        }
    }

    // 检查战斗结果
    function checkBattleResult() {
        try {
            // 使用战斗管理器获取统计信息
            const stats = battleManager.getBattleStats();
            const blueCount = stats.blue;  // 蓝方：玩家（白球国王 + 蓝球小兵）
            const redCount = stats.red;    // 红方：电脑（黑球Boss + 红球小兵）

            console.log(`当前战况 - 玩家（蓝方）:${blueCount}, 电脑（红方）:${redCount}`);

            if (redCount === 0 && blueCount > 0) {
                // 玩家获胜
                battleMode = false;
                showLevelEndScreen.call(this, true); // 显示胜利界面
                console.log('战斗结束：玩家获胜！');
            } else if (blueCount === 0 && redCount > 0) {
                // 电脑获胜
                battleMode = false;
                showLevelEndScreen.call(this, false); // 显示失败界面
                console.log('战斗结束：电脑获胜！');
            } else if (blueCount === 0 && redCount === 0) {
                // 平局（重新开始）
                battleMode = false;
                showLevelEndScreen.call(this, false); // 平局算失败
                console.log('战斗结束：平局！');
            }
            // 如果双方都还有单位，继续战斗，不做任何处理
        } catch (error) {
            console.warn('Error in checkBattleResult:', error);
        }
    }

    // 显示关卡结束界面
    function showLevelEndScreen(isVictory) {
        // 创建黑色半透明背景
        const overlay = this.add.graphics();
        overlay.fillStyle(0x000000, 0.7);
        overlay.fillRect(0, 0, 750, 1334);
        overlay.setDepth(100);

        // 创建结果面板
        const panelWidth = 400;
        const panelHeight = 300;
        const panelX = 375;
        const panelY = 667;

        const panel = this.add.graphics();
        panel.fillStyle(0xffffff, 1);
        panel.fillRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.lineStyle(4, isVictory ? 0x00ff00 : 0xff0000, 1);
        panel.strokeRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.setDepth(101);

        // 大emoji表情
        const emoji = isVictory ? '😊' : '😢';
        const emojiText = this.add.text(panelX, panelY - 80, emoji, {
            fontSize: '80px',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);

        // 成功/失败文字
        const resultText = isVictory ? '关卡成功！' : '关卡失败！';
        const resultColor = isVictory ? '#00ff00' : '#ff0000';
        const textResult = this.add.text(panelX, panelY - 10, resultText, {
            fontSize: '32px',
            fill: resultColor,
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);

        // 返回平台按钮
        const buttonText = '返回平台';
        const button = this.add.graphics();
        button.fillStyle(0x0066cc, 1);
        button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        button.lineStyle(2, 0x000000, 1);
        button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        button.setDepth(102);
        button.setInteractive(new Phaser.Geom.Rectangle(panelX - 80, panelY + 50, 160, 50), Phaser.Geom.Rectangle.Contains);

        const buttonTextObj = this.add.text(panelX, panelY + 75, buttonText, {
            fontSize: '24px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(103);

        // 按钮点击事件 - 返回平台游戏
        button.on('pointerdown', () => {
            returnToPlatformGame.call(this);
        });

        // 按钮悬停效果
        button.on('pointerover', () => {
            button.clear();
            button.fillStyle(0x0088ff, 1);
            button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        });

        button.on('pointerout', () => {
            button.clear();
            button.fillStyle(0x0066cc, 1);
            button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        });

        console.log(`显示关卡结束界面: ${isVictory ? '胜利' : '失败'}`);
    }

    // 返回平台游戏
    function returnToPlatformGame() {
        console.log('返回平台游戏');

        // 重置游戏状态
        gamePhase = 'platform';
        battleMode = false;
        isScrolling = true;

        // 清理战斗相关对象
        if (blueBalls) {
            blueBalls.forEach(ball => {
                if (ball && ball.destroy) {
                    if (ball.weapon && ball.weapon.destroy) ball.weapon.destroy();
                    if (ball.healthBar && ball.healthBar.destroy) ball.healthBar.destroy();
                    if (ball.healthBarBg && ball.healthBarBg.destroy) ball.healthBarBg.destroy();
                    ball.destroy();
                }
            });
            blueBalls = [];
        }

        if (redBalls) {
            redBalls.forEach(ball => {
                if (ball && ball.destroy) {
                    if (ball.weapon && ball.weapon.destroy) ball.weapon.destroy();
                    if (ball.healthBar && ball.healthBar.destroy) ball.healthBar.destroy();
                    if (ball.healthBarBg && ball.healthBarBg.destroy) ball.healthBarBg.destroy();
                    ball.destroy();
                }
            });
            redBalls = [];
        }

        // 清理战斗管理器
        if (battleManager) {
            battleManager.cleanup();
        }

        // 重新开始场景，回到平台游戏
        this.scene.restart();
    }

    // 更新武器和血条位置，让它们跟随角色移动
    function updateWeaponPositions() {
        // 如果正在变身中，跳过更新
        if (isTransforming) return;

        // 更新国王的武器和血条位置
        if (king && king.active && king.weapon && king.weapon.active && king.x !== undefined) {
            try {
                king.weapon.setPosition(king.x, king.y);
                if (king.healthBarBg && king.healthBarBg.active) {
                    king.healthBarBg.clear();
                    king.healthBarBg.fillStyle(0x000000);
                    king.healthBarBg.fillRect(king.x - 25, king.y - 35, 50, 6);
                }
                if (king.healthBar && king.healthBar.active) {
                    king.healthBar.clear();
                    king.healthBar.fillStyle(0x00ff00);
                    const healthPercent = king.health / king.maxHealth;
                    king.healthBar.fillRect(king.x - 25, king.y - 35, 50 * healthPercent, 6);
                }
            } catch (error) {
                console.warn('Error updating king position:', error);
            }
        }


        // 更新蓝色小兵的武器和血条位置
        blueBalls.filter(soldier => soldier !== null && soldier !== undefined).forEach((soldier, index) => {
            if (soldier && soldier.active && soldier.x !== undefined) {
                try {
                    // 更新武器位置（如果有武器）
                    if (soldier.weapon && soldier.weapon.active) {
                        soldier.weapon.setPosition(soldier.x + 8, soldier.y);
                    }

                    // 更新血条
                    if (soldier.healthBarBg && soldier.healthBarBg.active) {
                        soldier.healthBarBg.clear();
                        soldier.healthBarBg.fillStyle(0x000000);
                        soldier.healthBarBg.fillRect(soldier.x - 10, soldier.y - 15, 20, 3);
                    }
                    if (soldier.healthBar && soldier.healthBar.active) {
                        soldier.healthBar.clear();
                        soldier.healthBar.fillStyle(0x00ff00);
                        const healthPercent = soldier.health / soldier.maxHealth;
                        soldier.healthBar.fillRect(soldier.x - 10, soldier.y - 15, 20 * healthPercent, 3);
                    }
                } catch (error) {
                    console.warn(`Error updating blue soldier ${index} position:`, error);
                }
            }
        });

        // 更新红色小兵的武器和血条位置
        redBalls.filter(soldier => soldier !== null && soldier !== undefined).forEach((soldier, index) => {
            if (soldier && soldier.active && soldier.x !== undefined) {
                try {
                    // 更新武器位置（如果有武器）
                    if (soldier.weapon && soldier.weapon.active) {
                        soldier.weapon.setPosition(soldier.x - 8, soldier.y);
                    }

                    // 更新血条
                    if (soldier.healthBarBg && soldier.healthBarBg.active) {
                        soldier.healthBarBg.clear();
                        soldier.healthBarBg.fillStyle(0x000000);
                        soldier.healthBarBg.fillRect(soldier.x - 10, soldier.y - 15, 20, 3);
                    }
                    if (soldier.healthBar && soldier.healthBar.active) {
                        soldier.healthBar.clear();
                        soldier.healthBar.fillStyle(0xff0000);
                        const healthPercent = soldier.health / soldier.maxHealth;
                        soldier.healthBar.fillRect(soldier.x - 10, soldier.y - 15, 20 * healthPercent, 3);
                    }
                } catch (error) {
                    console.warn(`Error updating red soldier ${index} position:`, error);
                }
            }
        });

        // 更新红色将领的武器和血条位置
        blackBalls.filter(leader => leader !== null && leader !== undefined).forEach((leader, index) => {
            if (leader && leader.active && leader.x !== undefined) {
                try {
                    // 红色将领没有武器，只更新血条
                    if (leader.healthBarBg && leader.healthBarBg.active) {
                        leader.healthBarBg.clear();
                        leader.healthBarBg.fillStyle(0x000000);
                        leader.healthBarBg.fillRect(leader.x - 15, leader.y - 20, 30, 4);
                    }
                    if (leader.healthBar && leader.healthBar.active) {
                        leader.healthBar.clear();
                        leader.healthBar.fillStyle(0xff0000);
                        const healthPercent = leader.health / leader.maxHealth;
                        leader.healthBar.fillRect(leader.x - 15, leader.y - 20, 30 * healthPercent, 4);
                    }
                } catch (error) {
                    console.warn(`Error updating red leader ${index} position:`, error);
                }
            }
        });
    }

    // 处理待销毁的球 - 防止连锁反应bug
    function processBallDestruction() {
        try {
            // 确保待销毁数组存在
            if (!Array.isArray(ballsToDestroy)) ballsToDestroy = [];
            if (ballsToDestroy.length === 0) return;

            // 确保球数组存在
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];

        // 每帧只处理一个球，避免同时销毁多个球
        const ballToDestroy = ballsToDestroy.shift();

        if (ballToDestroy && ballToDestroy.active) {
            // 从对应数组中移除
            if (ballToDestroy.label === 'blueBall') {
                const index = blueBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    blueBalls.splice(index, 1);
                    console.log('销毁蓝球，剩余蓝球数量:', blueBalls.length);
                }
            } else if (ballToDestroy.label === 'redBall') {
                const index = redBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    redBalls.splice(index, 1);
                    console.log('销毁红球，剩余红球数量:', redBalls.length);
                }
            } else if (ballToDestroy.label === 'blackBall') {
                const index = blackBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    blackBalls.splice(index, 1);
                    console.log('销毁黑球，剩余黑球数量:', blackBalls.length);
                }
            }

            // 如果球有武器，也要销毁武器
            if (ballToDestroy.weapon) {
                ballToDestroy.weapon.destroy();
            }

            // 创建销毁特效
            const destroyEffect = this.add.text(ballToDestroy.x, ballToDestroy.y, '撞击!', {
                fontSize: '14px',
                fill: '#ffff00',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: destroyEffect,
                y: ballToDestroy.y - 40,
                alpha: 0,
                duration: 800,
                onComplete: () => destroyEffect.destroy()
            });

            // 销毁球
            ballToDestroy.destroy();
        }
        } catch (error) {
            console.warn('Error in processBallDestruction:', error);
        }
    }

    // 更新血条
    function updateHealthBars() {
        // 只在战斗阶段且血条存在时更新
        if (gamePhase === 'battle') {
            // 更新玩家血条
            if (this.playerHealthBar && this.playerHealthBar.clear) {
                this.playerHealthBar.clear();
                const playerHealthPercent = playerHealth / maxPlayerHealth;
                this.playerHealthBar.fillStyle(0x3498db);
                this.playerHealthBar.fillRoundedRect(100, 45, 150 * playerHealthPercent, 15, 7);
            }

            // 更新敌方血条
            if (this.enemyHealthBar && this.enemyHealthBar.clear) {
                this.enemyHealthBar.clear();
                const enemyHealthPercent = enemyHealth / maxEnemyHealth;
                this.enemyHealthBar.fillStyle(0xe74c3c);
                this.enemyHealthBar.fillRoundedRect(480, 45, 150 * enemyHealthPercent, 15, 7);
            }

            // 更新血条数值显示
            if (this.playerHealthText && this.playerHealthText.setText) {
                this.playerHealthText.setText(`${playerHealth}/${maxPlayerHealth}`);
            }
            if (this.enemyHealthText && this.enemyHealthText.setText) {
                this.enemyHealthText.setText(`${enemyHealth}/${maxEnemyHealth}`);
            }
        }
    }

    // 检查游戏状态
    function checkGameState() {
        try {
            // 确保数组存在
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];

            if (redBalls.length === 0 && blueBalls.length > 0) {
                if (this.statusText && this.statusText.setText) {
                    this.statusText.setText('红球全部清除 - 蓝方获胜！');
                    this.statusText.setStyle({ fill: '#0000ff', fontSize: '18px' });
                }
            } else if (blueBalls.length === 0 && redBalls.length > 0) {
                if (this.statusText && this.statusText.setText) {
                    this.statusText.setText('蓝球全部清除 - 红方获胜！');
                    this.statusText.setStyle({ fill: '#ff0000', fontSize: '18px' });
                }
            } else if (redBalls.length === 0 && blueBalls.length === 0) {
                if (this.statusText && this.statusText.setText) {
                    this.statusText.setText('所有球都清除了 - 平局！');
                    this.statusText.setStyle({ fill: '#ffff00', fontSize: '18px' });
                }
            }
        } catch (error) {
            console.warn('Error in checkGameState:', error);
        }
    }









    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
